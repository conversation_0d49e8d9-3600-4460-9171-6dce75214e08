import Link from 'next/link';

export default function Sidebar() {
  return (
    <nav className="sidebar">
      <Link href="/dashboard/attractive-score">
        <a className="sidebar-link">Attractive Score</a>
      </Link>
      <Link href="/dashboard/smile-rating">
        <a className="sidebar-link">Smile Rating</a>
      </Link>
    </nav>
  );
}

const styles = `
.sidebar {
  width: 200px;
  height: 100vh;
  background-color: #f0f0f0;
  padding: 20px;
  border-right: 1px solid #ddd;
}

.sidebar-link {
  display: block;
  padding: 10px;
  text-decoration: none;
  color: #333;
  transition: background-color 0.3s ease;
}

.sidebar-link:hover {
  background-color: #e0e0e0;
}
`;