// Script to process Amazon product data and insert into Supabase
const data = [
  {"searchQuery":"brightening serum","productId":"B091MGP94B","productName":"Good Molecules Daily Brightening Serum - Hyaluronic Acid Serum with Beta Arbutin to Target Dark Spots - Brightening and Anti-Aging Skin Care for Face","productPrice":7.97,"priceCurrencyCode":"USD","priceCurrencySymbol":"$","productUrl":"https://www.amazon.com/Good-Molecules-Brightening-Formulated-Hyaluronic/dp/B091MGP94B/ref=sr_1_1?dib=eyJ2IjoiMSJ9.2oIf2lS-_spzwyel8Ub872RACgJlDWxwE6rZ8Tfwbk8-L8CJxRypCJrzrF3xseRg_W-8VINoTU6-y42JgLHPe0i4yfUnxiJBJbDHB0B1FQRcy-p0ET1XrXnPEydK7QpuWmznS1V-7W9WkXYQo13d5L9Ysqz4oNvLKab0Je6nzWtgQcAEWVRPuP5VylUjn64Po7_O35pWjD2SKGvi2lBa570aLhhsuxa2bTKpEtZ4nruczPoPyk8dcjf3uw0z1idmT6cKkEuJxqAe-8cdB7ZrYmTjASamPRyf9-NiyxM6duw.jPzP10dpc3sB2bEvWYWq7rlNARjukWzeqdIwNQg1deU&dib_tag=se&keywords=brightening+serum&qid=1758527659&sr=8-1","productImage":"https://m.media-amazon.com/images/I/71L9F08R1+L._AC_UL320_.jpg","reviewCount":37,"isDiscountedProduct":false,"isSponsoredProduct":false,"scrapedAt":"2025-09-22T07:54:20.196Z","pageNumber":1},
  {"searchQuery":"brightening serum","productId":"B01EKUBU5Y","productName":"TruSkin Vitamin C Serum For Face – Anti Aging Face Serum with Vitamin C, Hyaluronic Acid, Vitamin E – Brightening Formula – Improve Appearance of Dark Spots, Tone, Fine Lines & Wrinkles, 2 Fl Oz","productPrice":38.99,"productPriceBeforeDiscount":48.99,"priceCurrencyCode":"USD","priceCurrencySymbol":"$","productUrl":"https://www.amazon.com/TruSkin-Naturals-Vitamin-Topical-Hyaluronic/dp/B01EKUBU5Y/ref=sr_1_4?dib=eyJ2IjoiMSJ9.2oIf2lS-_spzwyel8Ub872RACgJlDWxwE6rZ8Tfwbk8-L8CJxRypCJrzrF3xseRg_W-8VINoTU6-y42JgLHPe0i4yfUnxiJBJbDHB0B1FQRcy-p0ET1XrXnPEydK7QpuWmznS1V-7W9WkXYQo13d5L9Ysqz4oNvLKab0Je6nzWtgQcAEWVRPuP5VylUjn64Po7_O35pWjD2SKGvi2lBa570aLhhsuxa2bTKpEtZ4nruczPoPyk8dcjf3uw0z1idmT6cKkEuJxqAe-8cdB7ZrYmTjASamPRyf9-NiyxM6duw.jPzP10dpc3sB2bEvWYWq7rlNARjukWzeqdIwNQg1deU&dib_tag=se&keywords=brightening+serum&qid=1758527659&rdc=1&sr=8-4","productImage":"https://m.media-amazon.com/images/I/5122abFTZpL._AC_UL320_.jpg","reviewCount":1515,"isDiscountedProduct":true,"isSponsoredProduct":false,"scrapedAt":"2025-09-22T07:54:20.196Z","pageNumber":1}
];

// Function to generate semantic keywords from product name
function generateSemanticKeywords(productName, searchQuery) {
  const keywords = [];
  
  // Add search query
  keywords.push(searchQuery);
  
  // Extract key terms from product name
  const name = productName.toLowerCase();
  
  // Common skincare terms
  if (name.includes('vitamin c')) keywords.push('vitamin c');
  if (name.includes('hyaluronic acid')) keywords.push('hyaluronic acid');
  if (name.includes('niacinamide')) keywords.push('niacinamide');
  if (name.includes('retinol')) keywords.push('retinol');
  if (name.includes('serum')) keywords.push('serum');
  if (name.includes('brightening')) keywords.push('brightening');
  if (name.includes('anti aging') || name.includes('anti-aging')) keywords.push('anti aging');
  if (name.includes('dark spot')) keywords.push('dark spots');
  if (name.includes('wrinkle')) keywords.push('wrinkles');
  if (name.includes('fine line')) keywords.push('fine lines');
  if (name.includes('hydrating')) keywords.push('hydrating');
  if (name.includes('moisturizing')) keywords.push('moisturizing');
  if (name.includes('korean')) keywords.push('korean skincare');
  if (name.includes('face')) keywords.push('face care');
  if (name.includes('skin care') || name.includes('skincare')) keywords.push('skincare');
  
  // Remove duplicates
  return [...new Set(keywords)];
}

// Function to replace affiliate tag in URL
function addAffiliateTag(url, affiliateTag) {
  try {
    const urlObj = new URL(url);
    urlObj.searchParams.set('tag', affiliateTag);
    return urlObj.toString();
  } catch (e) {
    return url; // Return original URL if parsing fails
  }
}

// Filter and process data
const filteredData = data.filter(product => 
  product.productPrice >= 19 && product.reviewCount > 10
);

console.log(`Filtered ${filteredData.length} products from ${data.length} total products`);

// Process each product for Supabase insertion
const processedData = filteredData.map(product => ({
  product_id: product.productId,
  search_query: product.searchQuery,
  product_name: product.productName,
  product_price: product.productPrice,
  product_price_before_discount: product.productPriceBeforeDiscount || null,
  currency_code: product.priceCurrencyCode,
  currency_symbol: product.priceCurrencySymbol,
  product_url: addAffiliateTag(product.productUrl, 'amzleanmed-20'),
  product_image_url: product.productImage,
  review_count: product.reviewCount,
  is_discounted: product.isDiscountedProduct,
  is_sponsored: product.isSponsoredProduct,
  scraped_at: product.scrapedAt,
  page_number: product.pageNumber,
  semantic_keywords: generateSemanticKeywords(product.productName, product.searchQuery),
  primary_category: 'skincare',
  affiliate_tag: 'amzleanmed-20'
}));

console.log('Sample processed product:', JSON.stringify(processedData[0], null, 2));
console.log(`Total products to insert: ${processedData.length}`);

module.exports = { processedData };
