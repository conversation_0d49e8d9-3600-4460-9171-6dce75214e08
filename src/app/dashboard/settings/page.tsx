import { UserProfile } from "@clerk/nextjs";

export default function SettingsPage() {
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Account Settings</h1>
      <div className="max-w-3xl">
        <UserProfile
          routing="hash"
          appearance={{
            elements: {
              card: "shadow-none border-none",
              navbar: "hidden",
              navbarButton: "hidden",
            },
          }}
        />
      </div>
    </div>
  );
}
