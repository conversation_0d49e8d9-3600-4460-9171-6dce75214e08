import { auth } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import Sidebar from '../../components/dashboard/sidebar';
import FooterWrapper from '../../components/dashboard/FooterWrapper';

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { userId } = await auth();
    
  if (!userId) {
    redirect('/sign-in');
  }

  return (
    <section className="flex min-h-screen flex-col">
      <div className="flex flex-1">
        <Sidebar />
        <main className="flex-1 p-6">{children}</main>
      </div>
      <FooterWrapper />
    </section>
  );
}
