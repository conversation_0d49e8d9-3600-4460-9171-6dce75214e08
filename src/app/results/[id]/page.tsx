'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { Button } from '../../../components/ui/button';
import { Share2, Download, ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { toast } from 'sonner';
import MultiAnalysisResults from '../../../components/ui/multi-analysis-results';
import { generateAnalysisPDF } from '../../../lib/pdf-utils';
import SharedHeader from '../../../components/layout/SharedHeader';
import SharedFooter from '../../../components/layout/SharedFooter';
import ShareModal from '../../../components/ui/ShareModal';

interface AnalysisResult {
  id: string;
  analysis_data: any;
  image_url?: string;
  image_base64?: string;
  created_at: string;
}

export default function ResultsPage() {
  const params = useParams();
  const [result, setResult] = useState<AnalysisResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showShareModal, setShowShareModal] = useState(false);

  useEffect(() => {
    const fetchResult = async () => {
      try {
        const response = await fetch(`/api/results/${params.id}`);
        if (!response.ok) {
          throw new Error('Result not found');
        }
        const data = await response.json();
        setResult(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load result');
      } finally {
        setLoading(false);
      }
    };

    if (params.id) {
      fetchResult();
    }
  }, [params.id]);

  const handleShare = () => {
    setShowShareModal(true);
  };

  const handleDownload = async () => {
    if (!result) {
      toast.error('No analysis data available');
      return;
    }

    try {
      toast.info('Generating PDF report...');

      await generateAnalysisPDF({
        analysisData: result.analysis_data,
        imageBase64: result.image_base64,
        createdAt: result.created_at,
        resultId: params.id as string
      });

      toast.success('PDF report downloaded successfully!');
    } catch (error) {
      console.error('PDF generation failed:', error);
      toast.error('Failed to generate PDF. Please try again.');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-pink-50 to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-pink-500 mx-auto mb-4"></div>
          <p className="text-lg text-gray-600">Loading your analysis...</p>
        </div>
      </div>
    );
  }

  if (error || !result) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-pink-50 to-purple-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Analysis Not Found</h1>
          <p className="text-gray-600 mb-6">
            This analysis result may have expired or the link is invalid.
          </p>
          <Link href="/">
            <Button className="bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Home
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 to-purple-50">
      <SharedHeader />
      <div className="container mx-auto px-4 py-8 pt-24">
        {/* Header with actions */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
          <div>
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              Your Attractiveness Score Analysis
            </h1>
            <p className="text-gray-600">
              Generated on {new Date(result.created_at).toLocaleDateString()}
            </p>
          </div>
          
          <div className="flex gap-3">
            <Button
              onClick={handleShare}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Share2 className="h-4 w-4" />
              Share
            </Button>
            
            <Button
              onClick={handleDownload}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Download
            </Button>
            
            <Link href="/">
              <Button
                variant="outline"
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                New Analysis
              </Button>
            </Link>
          </div>
        </div>

        {/* Results Display */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <MultiAnalysisResults
            data={result.analysis_data}
            uploadedImage={result.image_base64 || result.image_url}
            isSharedView={true}
          />
        </div>

        {/* Action buttons at bottom */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center mt-8">
          <Link href="/">
            <Button
              size="lg"
              className="w-full sm:w-auto px-8 py-3 text-lg font-semibold bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700"
            >
              Analyze Another Photo
            </Button>
          </Link>
          
          <Button
            onClick={handleShare}
            size="lg"
            variant="outline"
            className="w-full sm:w-auto px-8 py-3 text-lg font-semibold border-2 border-pink-500 text-pink-600 hover:bg-pink-50"
          >
            <Share2 className="mr-2 h-5 w-5" />
            Share Results
          </Button>
        </div>
      </div>
      <SharedFooter />

      {showShareModal && (
        <ShareModal
          shareUrl={window.location.href}
          onClose={() => setShowShareModal(false)}
        />
      )}
    </div>
  );
}
