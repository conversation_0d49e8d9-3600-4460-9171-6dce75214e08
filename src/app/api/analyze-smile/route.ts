import { NextResponse } from 'next/server'
import { PROMPTS } from '../../../lib/prompts'

export const runtime = 'edge'

export async function POST(request: Request) {
  try {
    const formData = await request.formData()
    const image = formData.get('image') as File
    
    if (!image) {
      return NextResponse.json(
        { error: 'No image provided' },
        { status: 400 }
      )
    }

    // Convert image to base64
    const buffer = await image.arrayBuffer()
    const base64Image = Buffer.from(buffer).toString('base64')

    // Call AI service for smile analysis
    const aiResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'anthropic/claude-3-opus',
        messages: [{
          role: 'user',
          content: PROMPTS.smileAnalysis
        }],
        attachments: [{
          data: base64Image,
          mime_type: image.type
        }]
      })
    })

    const data = await aiResponse.json()
    
    return NextResponse.json(data)
    
  } catch (error) {
    console.error('Smile analysis error:', error)
    return NextResponse.json(
      { error: 'Failed to analyze smile' },
      { status: 500 }
    )
  }
}
