"use client";

"use client";

import { useRouter } from "next/navigation";
import { SignIn, useUser } from "@clerk/nextjs";
import { useEffect, useState } from "react";

export default function SignInWrapper() {
  const router = useRouter();
  const { isLoaded, isSignedIn } = useUser();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && isLoaded && isSignedIn) {
      router.push("/dashboard");
    }
  }, [mounted, isLoaded, isSignedIn, router]);

  if (!mounted) return null;

  return (
    <SignIn 
      afterSignInUrl="/dashboard"
      afterSignUpUrl="/dashboard"
      redirectUrl="/dashboard"
    />
  );
}
