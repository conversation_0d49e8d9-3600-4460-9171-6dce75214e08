'use client'

import Script from 'next/script'

export default function UmamiAnalytics() {
  // Get Umami configuration from environment variables
  const umamiScriptUrl = process.env.NEXT_PUBLIC_UMAMI_SCRIPT_URL
  const umamiWebsiteId = process.env.NEXT_PUBLIC_UMAMI_WEBSITE_ID

  // Only render the script if both environment variables are set
  if (!umamiScriptUrl || !umamiWebsiteId) {
    console.warn('Umami Analytics: Missing environment variables NEXT_PUBLIC_UMAMI_SCRIPT_URL or NEXT_PUBLIC_UMAMI_WEBSITE_ID')
    return null
  }

  return (
    <Script
      id="umami-analytics-script"
      strategy="afterInteractive"
      src={umamiScriptUrl}
      data-website-id={umamiWebsiteId}
      defer
    />
  )
}
