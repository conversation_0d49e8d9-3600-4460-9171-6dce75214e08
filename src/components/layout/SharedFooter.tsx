"use client";

import Link from "next/link";
import { Camera } from "lucide-react";
import { useEffect, useState } from "react";

const SharedFooter = () => {
  const [year, setYear] = useState(new Date().getFullYear());

  useEffect(() => {
    setYear(new Date().getFullYear());
  }, []);

  return (
    <footer className="border-t py-12">
      <div className="container flex flex-col items-center gap-4 text-center">
        <div className="flex items-center gap-2 font-bold">
          <Camera className="h-6 w-6" />
          <span>Attractiveness Score</span>
        </div>
        <div className="flex gap-4 text-sm text-muted-foreground">
          <Link href="/privacy-policy" className="hover:underline">
            Privacy Policy
          </Link>
          <Link href="/terms-of-service" className="hover:underline">
            Terms of Service
          </Link>
        </div>
        <p className="text-sm text-muted-foreground">
          © {year} Attractiveness Score. All rights reserved.
        </p>
      </div>
    </footer>
  );
};

export default SharedFooter;
