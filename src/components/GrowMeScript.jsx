'use client'

import Script from 'next/script'

export default function GrowMeScript() {
  return (
    <Script
      id="growme-script"
      strategy="afterInteractive"
      dangerouslySetInnerHTML={{
        __html: `
          !(function() {
            window.growMe || ((window.growMe = function(e) {
              window.growMe._.push(e);
            }), (window.growMe._ = []));
            var e = document.createElement('script');
            e.type = 'text/javascript';
            e.src = 'https://faves.grow.me/main.js';
            e.defer = true;
            e.setAttribute('data-grow-faves-site-id', 'U2l0ZTplOGY0Nzc1MS05YjI0LTRlNTktYmI0Ni02ZTBmMTFkMGY2YWQ=');
            var t = document.getElementsByTagName('script')[0];
            t.parentNode.insertBefore(e, t);
            console.log('GrowMe script loaded');
          })();
        `
      }}
    />
  )
}
