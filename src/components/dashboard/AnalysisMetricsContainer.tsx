import { AnalysisMetric } from './AnalysisMetric';

interface AnalysisMetricsContainerProps {
  metrics: Array<{
    name: string;
    score: number;
    description?: string;
  }>;
  maxScore?: number;
}

export function AnalysisMetricsContainer({ 
  metrics, 
  maxScore = 10 
}: AnalysisMetricsContainerProps) {
  return (
    <div className="space-y-4">
      {metrics.map((metric, index) => (
        <AnalysisMetric
          key={index}
          name={metric.name}
          score={metric.score}
          description={metric.description}
          maxScore={maxScore}
        />
      ))}
    </div>
  );
}
