"use client";

import { Card } from "../ui/card";
import { Button } from "../ui/button";
import { useState, useEffect } from "react";
import { Loader2 } from "lucide-react";
import Image from "next/image";
import { AnalysisMetricsContainer } from "./AnalysisMetricsContainer";
import { formatRecommendationsAsPoints } from "../../lib/recommendation-utils";

interface Factor {
  name: string;
  score: number;
  description: string;
  recommendation?: string;
}

interface AnalysisResult {
  overall_score: number;
  factors: Factor[];
  analysis_text: string;
  overall_recommendations?: string;
}

export function SmileRating() {
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [result, setResult] = useState<AnalysisResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) {
        setError("Image size must be less than 5MB");
        return;
      }
      if (!file.type.startsWith("image/")) {
        setError("Please upload a valid image file");
        return;
      }

      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }

      setSelectedImage(file);
      setPreviewUrl(URL.createObjectURL(file));
      setResult(null);
      setError(null);
    }
  };

  const handleAnalysis = async () => {
    if (!selectedImage) return;

    setIsAnalyzing(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append("image", selectedImage);
      formData.append("analysisType", "smile-rating");

      const response = await fetch("/api/analyze-image", {
        method: "POST",
        body: formData,
      });

      let data;
      try {
        data = await response.json();
      } catch (error) {
        throw new Error("Failed to parse server response");
      }

      if (!response.ok || data.error) {
        throw new Error(data?.error || "Failed to analyze image");
      }

      setResult(data);
    } catch (error) {
      console.error("Analysis error:", error);
      setError(error instanceof Error ? error.message : "Failed to analyze image. Please try again.");
    } finally {
      setIsAnalyzing(false);
    }
  };

  const smileFactors = [
    { name: "Genuineness", score: 0, description: "", recommendation: "" },
    { name: "Symmetry", score: 0, description: "", recommendation: "" },
    { name: "Teeth Appearance", score: 0, description: "", recommendation: "" },
    { name: "Facial Proportions", score: 0, description: "", recommendation: "" },
    { name: "Confidence", score: 0, description: "", recommendation: "" },
    { name: "Emotional Resonance", score: 0, description: "", recommendation: "" }
  ];

  return (
    <div className="max-w-4xl mx-auto space-y-8 p-4">
      <Card className="p-8 space-y-8">
        <div className="space-y-3 text-center">
          <h2 className="text-3xl font-bold tracking-tight">Smile Rating</h2>
          <p className="text-lg text-muted-foreground">
            Upload a clear photo of a smile to analyze its quality
          </p>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-center w-full">
            <label
              htmlFor="image-upload"
              className="flex flex-col items-center justify-center w-full h-64 border-2 border-dashed rounded-lg cursor-pointer hover:bg-accent/50 transition-colors relative overflow-hidden"
            >
              {previewUrl ? (
                <div className="absolute inset-0 w-full h-full">
                  <Image
                    src={previewUrl}
                    alt="Preview"
                    fill
                    className="object-contain"
                    sizes="(max-width: 768px) 100vw, 768px"
                    priority
                  />
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center pt-5 pb-6">
                  <svg
                    className="w-8 h-8 mb-4 text-muted-foreground"
                    aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 20 16"
                  >
                    <path
                      stroke="currentColor"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"
                    />
                  </svg>
                  <p className="mb-2 text-sm text-muted-foreground">
                    <span className="font-semibold">Click to upload</span> or drag and drop
                  </p>
                  <p className="text-xs text-muted-foreground">
                    PNG, JPG, JPEG (MAX. 5MB)
                  </p>
                </div>
              )}
              <input
                id="image-upload"
                type="file"
                className="hidden"
                accept="image/png, image/jpeg"
                onChange={handleImageUpload}
              />
            </label>
          </div>

          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-3 rounded-md text-sm">
              {error}
            </div>
          )}

          <div className="flex gap-4">
            <Button
              className="flex-1"
              disabled={!selectedImage || isAnalyzing}
              onClick={handleAnalysis}
            >
              {isAnalyzing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Analyzing...
                </>
              ) : (
                "Analyze Now"
              )}
            </Button>
            {(selectedImage || result) && (
              <Button
                variant="outline"
                onClick={() => {
                  setSelectedImage(null);
                  setPreviewUrl(null);
                  setResult(null);
                  setError(null);
                }}
              >
                Reset
              </Button>
            )}
          </div>
        </div>
      </Card>

      {result && (
        <>
          <Card className="p-8 space-y-8">
            <div className="text-center space-y-4">
              <div className="space-y-4">
                <h3 className="text-4xl font-bold tracking-tight">
                  Overall Score: {(Math.round(result.overall_score * 10) / 10).toFixed(1)}/10
                </h3>
              </div>
              <p className="text-xl text-muted-foreground italic leading-relaxed">
                &ldquo;{result.analysis_text}&rdquo;
              </p>
            </div>

            <AnalysisMetricsContainer 
              metrics={result.factors}
            />
          </Card>

          <Card className="p-8 space-y-8">
            <h3 className="text-2xl font-bold tracking-tight">Recommendations</h3>
            <div className="space-y-8">
              {result.factors.map((factor, index) => (
                <div key={index} className="space-y-3 p-4 rounded-lg bg-accent/10">
                  <h4 className="text-xl font-semibold tracking-tight">{factor.name}</h4>
                  <p className="text-lg text-muted-foreground leading-relaxed">{factor.recommendation}</p>
                </div>
              ))}
              {result.overall_recommendations && (
                <div className="pt-6 border-t space-y-4">
                  <h4 className="text-xl font-semibold tracking-tight">Overall Recommendations</h4>
                  <div className="space-y-3">
                    {formatRecommendationsAsPoints(result.overall_recommendations).map((point, index) => (
                      <div key={index} className="flex items-start gap-3">
                        <span className="font-semibold text-pink-600 text-lg min-w-[28px]">{index + 1})</span>
                        <span className="text-lg text-muted-foreground leading-relaxed flex-1">{point}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </Card>
        </>
      )}

      {!result && (
        <Card className="p-8">
          <h3 className="text-2xl font-bold tracking-tight mb-6">Analysis Factors</h3>
          <AnalysisMetricsContainer 
            metrics={smileFactors}
          />
        </Card>
      )}
    </div>
  );
}
