"use client";

import React, { useState } from "react";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import {
  LogOutIcon,
  Menu,
  StarIcon,
  Settings,
  SmileIcon
} from "lucide-react";
import Link from "next/link";
import { SignOutButton, useUser } from "@clerk/nextjs";

export default function Sidebar() {
  const [isOpen, setIsOpen] = useState(false);
  const { user } = useUser();

  return (
    <>
      <button
        className="lg:hidden fixed top-4 left-4 z-20 px-4 py-2 rounded-md bg-gray-800 text-white hover:bg-gray-700 transition-colors"
        onClick={() => setIsOpen(!isOpen)}
      >
        Dashboard
      </button>

      <aside
        className={`
        fixed inset-y-0 left-0 z-10 w-64 p-4 border-r transform transition-transform duration-300 ease-in-out
        ${isOpen ? "translate-x-0" : "-translate-x-full"}
        lg:translate-x-0 lg:static lg:block
        bg-[#f1f5f9] dark:bg-[#111a2d]
      `}
      >
        <div className="border-b p-8 ">
          <div className="flex flex-col items-center">
            <Avatar className="w-16 h-16 mb-4">
              <AvatarImage src={user?.imageUrl || "https://github.com/shadcn.png"} alt="User Avatar" />
              <AvatarFallback>{user?.firstName?.charAt(0) || "U"}</AvatarFallback>
            </Avatar>
            <div className="text-center">
              <p className="text-lg font-semibold">{user?.fullName || "User"}</p>
              <p className="text-sm text-muted-foreground">Free Plan</p>
              {/* <Button variant="default" size="sm" className="mt-2">
                Upgrade
              </Button> */}
            </div>
          </div>
        </div>

        <nav className="mt-2">
          <div className="px-8 pb-6 border-b">
            <p className="text-sm font-semibold mt-6">Features</p>
            <ul className="space-y-2 mt-2">
              <li className="flex items-center space-x-2">
                <StarIcon className="w-4 h-4" />
                <Link
                  href="/dashboard/attractive-score"
                  className="text-muted-foreground hover:text-foreground transition-colors"
                >
                  Attractive Score
                </Link>
              </li>
              <li className="flex items-center space-x-2">
                <SmileIcon className="w-4 h-4" />
                <Link
                  href="/dashboard/smile-rating"
                  className="text-muted-foreground hover:text-foreground transition-colors"
                >
                  Smile Rating
                </Link>
              </li>
              </ul>
              <p className="text-sm font-semibold mt-6">User Options</p>
              <ul className="space-y-2 mt-2">
                <li className="flex items-center space-x-2">
                  <Settings className="w-4 h-4" />
                  <Link
                    href="/dashboard/settings"
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    Settings
                  </Link>
                </li>
                <li className="flex items-center space-x-2 cursor-pointer">
                  <LogOutIcon className="w-4 h-4" />
                  <SignOutButton redirectUrl="/">
                    <span className="text-muted-foreground hover:text-foreground transition-colors">
                      Log Out
                    </span>
                  </SignOutButton>
                </li>
            </ul>
          </div>
          <div className="mt-4 p-8"></div>
        </nav>
      </aside>
    </>
  );
}
