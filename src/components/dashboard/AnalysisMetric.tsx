interface AnalysisMetricProps {
  name: string;
  score: number;
  description?: string;
  maxScore?: number;
}

export function AnalysisMetric({ 
  name, 
  score, 
  description, 
  maxScore = 10 
}: AnalysisMetricProps) {
  const percentage = (score / maxScore) * 100;

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <span className="text-lg font-semibold tracking-tight">{name}</span>
        <span className="text-lg font-medium text-primary">{score}/{maxScore}</span>
      </div>
      <div className="h-3 bg-accent/20 rounded-full overflow-hidden">
        <div 
          className="h-full bg-primary rounded-full transition-all duration-500 ease-out" 
          style={{ width: `${percentage}%` }}
        />
      </div>
      {description && (
        <p className="text-base text-muted-foreground leading-relaxed">{description}</p>
      )}
    </div>
  );
}
