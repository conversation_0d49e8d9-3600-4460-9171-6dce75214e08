"use client";

import { <PERSON><PERSON> } from "../../components/ui/button";
import { Menu, Camera, X } from "lucide-react";
import Link from "next/link";
import { SignInButton, SignUpButton, useUser } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";

const Navbar = () => {
  const router = useRouter();
  const { isSignedIn } = useUser();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    if (isSignedIn) {
      router.push('/dashboard');
    }
  }, [isSignedIn, router]);

  return (
    <header className="fixed top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center justify-between">
        <div className="flex items-center gap-2 font-bold">
          <Camera className="h-5 w-5 md:h-6 md:w-6" />
          <Link href="/" className="text-base md:text-xl">
            Attractiveness Score
          </Link>
        </div>
        
        {/* Desktop Navigation */}
        <nav className="flex items-center gap-2 md:gap-4">
          {isSignedIn && (
            <Button asChild size="sm" className="text-sm">
              <Link href="/dashboard">Dashboard</Link>
            </Button>
          )}
        </nav>
      </div>
    </header>
  );
};

export default Navbar;
