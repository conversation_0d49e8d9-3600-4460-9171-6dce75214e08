import { Users, Target, Shield, Award } from "lucide-react";

const About = () => {
  return (
    <section className="py-16 bg-background" id="about-us">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl mb-8 text-foreground">
            About Attractive Score
          </h2>

          <div className="space-y-6 mb-12">
            <div className="max-w-3xl mx-auto">
              <p className="text-lg text-muted-foreground leading-relaxed">
                AttractiveScore is an innovative AI-powered platform designed to help people understand and enhance their natural beauty through advanced computer vision and machine learning technology. We combine scientific analysis with cutting-edge AI to provide accurate, personalized insights into facial features and attractiveness.
              </p>

              <p className="text-lg text-muted-foreground leading-relaxed mt-6">
                Our platform democratizes professional beauty analysis, making it accessible for everyone to discover their unique potential through unbiased, scientific assessment. We prioritize privacy with client-side processing whenever possible and never store personal photos without explicit consent.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
