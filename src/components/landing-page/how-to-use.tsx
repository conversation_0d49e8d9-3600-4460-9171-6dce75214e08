import { Upload, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Target, Lightbulb } from "lucide-react";

const HowToUse = () => {
  const steps = [
    {
      icon: <Upload className="h-8 w-8 text-primary" />,
      title: "Upload Your Photo",
      description: "Simply upload a clear photo of your face. Our system accepts JPG and PNG files up to 5MB. No face data is stored for your privacy.",
      step: "01"
    },
    {
      icon: <Brain className="h-8 w-8 text-primary" />,
      title: "AI Analysis",
      description: "Our advanced AI analyzes 9 key facial factors including bone structure, symmetry, and features using scientific beauty standards.",
      step: "02"
    },
    {
      icon: <BarChart3 className="h-8 w-8 text-primary" />,
      title: "Get Your Score",
      description: "Receive an overall attractiveness score plus detailed ratings for each factor, helping you understand your unique facial characteristics.",
      step: "03"
    },
    {
      icon: <Target className="h-8 w-8 text-primary" />,
      title: "Detailed Breakdown",
      description: "Explore in-depth analysis of your facial symmetry, jawline, cheekbones, eyes, and other key features that contribute to attractiveness.",
      step: "04"
    },
    {
      icon: <Lightbulb className="h-8 w-8 text-primary" />,
      title: "Improvement Tips",
      description: "Get personalized, natural enhancement recommendations based on your specific features - no surgery suggestions, just practical tips.",
      step: "05"
    }
  ];

  return (
    <section className="py-24 bg-muted/30" id="how-to-use">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl mb-4">
            How To Use Attractive Score
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Get your comprehensive facial analysis in just a few simple steps
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {steps.map((step, index) => (
            <div
              key={index}
              className="relative bg-background rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-300 border border-border/50"
            >
              {/* Step Number */}
              <div className="absolute -top-4 -left-4 w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">
                {step.step}
              </div>
              
              {/* Icon */}
              <div className="mb-4">
                {step.icon}
              </div>
              
              {/* Content */}
              <h3 className="text-xl font-semibold mb-3 text-foreground">
                {step.title}
              </h3>
              <p className="text-muted-foreground leading-relaxed">
                {step.description}
              </p>
            </div>
          ))}
        </div>

        {/* Additional Info */}
        <div className="mt-16 text-center">
          <div className="inline-flex items-center gap-2 bg-background/80 backdrop-blur-sm rounded-full px-6 py-3 border border-border/50">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-muted-foreground">
              Analysis typically takes 10-30 seconds
            </span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HowToUse;
