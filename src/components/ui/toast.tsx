import * as React from "react";
import { ToastProps, ToastProvider, ToastViewport } from "@radix-ui/react-toast";
import { X } from "lucide-react";

interface ToastComponentProps extends ToastProps {
  title?: string;
  description?: string;
  variant?: "default" | "destructive";
}

const Toast = React.forwardRef<HTMLDivElement, ToastComponentProps>(
  ({ title, description, variant = "default", ...props }, ref) => {
    return (
      <ToastProvider>
        <div
          ref={ref}
          className={`group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all ${
            variant === "destructive"
              ? "border-red-500 bg-red-50 text-red-900"
              : "border-border bg-background text-foreground"
          }`}
          {...props as React.HTMLAttributes<HTMLDivElement>}
        >
          <div className="flex-1 space-y-1">
            {title && <p className="font-medium">{title}</p>}
            {description && (
              <p className="text-sm opacity-90">{description}</p>
            )}
          </div>
          <button className="absolute right-2 top-2 rounded-md p-1 opacity-0 transition-opacity focus:opacity-100 group-hover:opacity-100">
            <X className="h-4 w-4" />
          </button>
        </div>
        <ToastViewport className="fixed bottom-0 right-0 z-[100] flex w-[390px] max-w-[100vw] flex-col gap-2 p-6" />
      </ToastProvider>
    );
  }
);
Toast.displayName = "Toast";

export { Toast };

export function useToast() {
  const [toasts, setToasts] = React.useState<ToastComponentProps[]>([]);

  const toast = (props: ToastComponentProps) => {
    setToasts((prev) => [...prev, props]);
    setTimeout(() => {
      setToasts((prev) => prev.slice(1));
    }, 3000);
  };

  return {
    toast,
    toasts,
  };
}
