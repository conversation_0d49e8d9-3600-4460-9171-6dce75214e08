"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { Loader2 } from "lucide-react"

interface ProgressProps extends React.HTMLAttributes<HTMLDivElement> {
  value?: number
  max?: number
  showPercentage?: boolean
  showSpinner?: boolean
  message?: string
  variant?: "default" | "gradient"
}

const Progress = React.forwardRef<HTMLDivElement, ProgressProps>(
  ({ 
    className, 
    value = 0, 
    max = 100, 
    showPercentage = true, 
    showSpinner = false,
    message,
    variant = "default",
    ...props 
  }, ref) => {
    const percentage = Math.min(Math.max((value / max) * 100, 0), 100)
    
    return (
      <div
        ref={ref}
        className={cn("w-full space-y-3", className)}
        {...props}
      >
        {/* Message and spinner */}
        {(message || showSpinner) && (
          <div className="flex items-center justify-center space-x-2">
            {showSpinner && (
              <Loader2 className="h-4 w-4 animate-spin text-pink-500" />
            )}
            {message && (
              <p className="text-sm font-medium text-gray-700">{message}</p>
            )}
          </div>
        )}
        
        {/* Progress bar */}
        <div className="relative">
          <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
            <div
              className={cn(
                "h-full transition-all duration-500 ease-out rounded-full",
                variant === "gradient" 
                  ? "bg-gradient-to-r from-pink-500 to-purple-600" 
                  : "bg-pink-500"
              )}
              style={{ width: `${percentage}%` }}
            />
          </div>
          
          {/* Percentage display */}
          {showPercentage && (
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-xs font-semibold text-gray-700">
                {Math.round(percentage)}%
              </span>
            </div>
          )}
        </div>
      </div>
    )
  }
)

Progress.displayName = "Progress"

export { Progress }
