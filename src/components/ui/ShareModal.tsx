'use client';

import { Button } from './button';
import { X, Co<PERSON>, Check } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

interface ShareModalProps {
  shareUrl: string;
  onClose: () => void;
}

const ShareModal = ({ shareUrl, onClose }: ShareModalProps) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      if (navigator.clipboard && navigator.clipboard.writeText) {
        await navigator.clipboard.writeText(shareUrl);
        setCopied(true);
        toast.success('Link copied to clipboard!');
        setTimeout(() => setCopied(false), 2000);
      } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = shareUrl;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
          document.execCommand('copy');
          setCopied(true);
          toast.success('Link copied to clipboard!');
          setTimeout(() => setCopied(false), 2000);
        } catch (fallbackErr) {
          toast.error('Copy failed. Please select and copy manually.');
        } finally {
          document.body.removeChild(textArea);
        }
      }
    } catch (err) {
      console.error('Copy failed:', err);
      toast.error('Failed to copy link. Please select and copy manually.');
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div className="flex justify-between items-center p-6 border-b">
          <h3 className="text-lg font-semibold">Share Your Analysis</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        <div className="p-6">
          <p className="text-gray-600 mb-4">
            Copy the link below to share your attractiveness analysis results with others:
          </p>

          <div className="flex gap-2">
            <input
              type="text"
              value={shareUrl}
              readOnly
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
            />
            <Button
              onClick={handleCopy}
              variant="outline"
              className="flex items-center gap-2 whitespace-nowrap"
            >
              {copied ? <Check size={16} className="text-green-500" /> : <Copy size={16} />}
              {copied ? 'Copied!' : 'Copy'}
            </Button>
          </div>

          <div className="mt-4 p-3 bg-blue-50 rounded-md">
            <p className="text-sm text-blue-800">
              <strong>You can share this link with:</strong><br />
              • Friends and family<br />
              • Social media platforms<br />
              • Anyone you want to share your analysis with
            </p>
          </div>
        </div>

        <div className="flex justify-end p-6 border-t bg-gray-50">
          <Button onClick={onClose} className="bg-pink-500 hover:bg-pink-600">
            Done
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ShareModal;
