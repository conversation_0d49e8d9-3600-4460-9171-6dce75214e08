/**
 * Utility functions for image compression and conversion
 */

export interface CompressedImageResult {
  base64: string;
  size: number;
  originalSize: number;
  compressionRatio: number;
}

/**
 * Compresses an image file and converts it to base64
 * @param file - The image file to compress
 * @param maxWidth - Maximum width for the compressed image (default: 1024)
 * @param maxHeight - Maximum height for the compressed image (default: 1024)
 * @param quality - JPEG quality (0.1 to 1.0, default: 0.8)
 * @returns Promise with compressed image data
 */
export async function compressImageToBase64(
  file: File,
  maxWidth: number = 1024,
  maxHeight: number = 1024,
  quality: number = 0.8
): Promise<CompressedImageResult> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    if (!ctx) {
      reject(new Error('Could not get canvas context'));
      return;
    }

    img.onload = () => {
      // Calculate new dimensions while maintaining aspect ratio
      let { width, height } = img;
      
      if (width > height) {
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }
      } else {
        if (height > maxHeight) {
          width = (width * maxHeight) / height;
          height = maxHeight;
        }
      }

      // Set canvas dimensions
      canvas.width = width;
      canvas.height = height;

      // Draw and compress the image
      ctx.drawImage(img, 0, 0, width, height);
      
      // Convert to base64 with compression
      const base64 = canvas.toDataURL('image/jpeg', quality);
      
      // Calculate compression stats
      const originalSize = file.size;
      const compressedSize = Math.round((base64.length * 3) / 4); // Approximate size of base64
      const compressionRatio = ((originalSize - compressedSize) / originalSize) * 100;

      resolve({
        base64,
        size: compressedSize,
        originalSize,
        compressionRatio
      });
    };

    img.onerror = () => {
      reject(new Error('Failed to load image'));
    };

    // Create object URL for the image
    img.src = URL.createObjectURL(file);
  });
}

/**
 * Validates if a file is a valid image
 * @param file - The file to validate
 * @returns boolean indicating if the file is a valid image
 */
export function isValidImageFile(file: File): boolean {
  const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  return validTypes.includes(file.type.toLowerCase());
}

/**
 * Validates image file size
 * @param file - The file to validate
 * @param maxSizeMB - Maximum size in MB (default: 10)
 * @returns boolean indicating if the file size is valid
 */
export function isValidImageSize(file: File, maxSizeMB: number = 10): boolean {
  const maxSizeBytes = maxSizeMB * 1024 * 1024;
  return file.size <= maxSizeBytes;
}

/**
 * Converts a base64 string to a File object
 * @param base64 - The base64 string
 * @param filename - The filename for the file
 * @param mimeType - The MIME type (default: 'image/jpeg')
 * @returns File object
 */
export function base64ToFile(base64: string, filename: string, mimeType: string = 'image/jpeg'): File {
  // Remove data URL prefix if present
  const base64Data = base64.replace(/^data:image\/[a-z]+;base64,/, '');
  
  // Convert base64 to binary
  const binaryString = atob(base64Data);
  const bytes = new Uint8Array(binaryString.length);
  
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  
  return new File([bytes], filename, { type: mimeType });
}

/**
 * Gets image dimensions from a file
 * @param file - The image file
 * @returns Promise with width and height
 */
export function getImageDimensions(file: File): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight
      });
    };
    
    img.onerror = () => {
      reject(new Error('Failed to load image for dimension calculation'));
    };
    
    img.src = URL.createObjectURL(file);
  });
}
