/**
 * Utility functions for processing and formatting recommendations
 */

/**
 * Converts paragraph text into numbered points (max 5 points)
 * @param text - The recommendation text to format
 * @returns Array of formatted recommendation points
 */
export function formatRecommendationsAsPoints(text: string): string[] {
  if (!text || text.trim().length === 0) {
    return [];
  }

  // Split by common sentence delimiters and clean up
  const sentences = text
    .split(/[.!?]+/)
    .map(sentence => sentence.trim())
    .filter(sentence => sentence.length > 10) // Filter out very short fragments
    .slice(0, 5); // Limit to max 5 points

  // If we have very few sentences, try splitting by other patterns
  if (sentences.length <= 2) {
    const alternativeSplit = text
      .split(/[;,](?=\s*[A-Z])/) // Split on semicolons or commas followed by capital letters
      .map(sentence => sentence.trim())
      .filter(sentence => sentence.length > 10)
      .slice(0, 5);
    
    if (alternativeSplit.length > sentences.length) {
      return alternativeSplit.map(sentence => sentence.replace(/[.!?]+$/, ''));
    }
  }

  return sentences.map(sentence => sentence.replace(/[.!?]+$/, ''));
}

/**
 * Extracts specific ingredients/components mentioned in recommendations
 * @param recommendations - The recommendation text
 * @returns Array of specific ingredients/components found
 */
export function extractSpecificIngredients(recommendations: string): string[] {
  const lowerText = recommendations.toLowerCase();
  const ingredients: string[] = [];

  // Common skincare ingredients
  const ingredientPatterns = [
    { pattern: /hyaluronic\s+acid/g, ingredient: 'hyaluronic acid' },
    { pattern: /vitamin\s+c/g, ingredient: 'vitamin C' },
    { pattern: /vitamin\s+e/g, ingredient: 'vitamin E' },
    { pattern: /retinol/g, ingredient: 'retinol' },
    { pattern: /niacinamide/g, ingredient: 'niacinamide' },
    { pattern: /salicylic\s+acid/g, ingredient: 'salicylic acid' },
    { pattern: /glycolic\s+acid/g, ingredient: 'glycolic acid' },
    { pattern: /peptides?/g, ingredient: 'peptides' },
    { pattern: /collagen/g, ingredient: 'collagen' },
    { pattern: /ceramides?/g, ingredient: 'ceramides' },
    { pattern: /antioxidants?/g, ingredient: 'antioxidants' },
    { pattern: /spf|sunscreen|sun\s+protection/g, ingredient: 'SPF' },
    { pattern: /moisturiz/g, ingredient: 'moisturizer' },
    { pattern: /serum/g, ingredient: 'serum' },
    { pattern: /cleanser/g, ingredient: 'cleanser' },
    { pattern: /exfoliat/g, ingredient: 'exfoliant' }
  ];

  ingredientPatterns.forEach(({ pattern, ingredient }) => {
    if (pattern.test(lowerText)) {
      ingredients.push(ingredient);
    }
  });

  return Array.from(new Set(ingredients)); // Remove duplicates
}

/**
 * Extracts key phrases from recommendations for product description matching
 * @param recommendations - The recommendation text
 * @returns Array of key phrases that can be used in product descriptions
 */
export function extractKeyPhrasesForDescriptions(recommendations: string): string[] {
  const lowerText = recommendations.toLowerCase();
  const phrases: string[] = [];

  // Common benefit phrases
  const benefitPatterns = [
    { pattern: /antioxidant\s+protection/g, phrase: 'antioxidant protection' },
    { pattern: /brightening/g, phrase: 'brightening' },
    { pattern: /hydrat(ing|ion)/g, phrase: 'hydration' },
    { pattern: /anti[- ]aging/g, phrase: 'anti-aging' },
    { pattern: /skin\s+radiance/g, phrase: 'skin radiance' },
    { pattern: /glow/g, phrase: 'glow' },
    { pattern: /firmness/g, phrase: 'firmness' },
    { pattern: /elasticity/g, phrase: 'elasticity' },
    { pattern: /moisture/g, phrase: 'moisture' },
    { pattern: /nourish/g, phrase: 'nourishment' },
    { pattern: /repair/g, phrase: 'repair' },
    { pattern: /protect/g, phrase: 'protection' },
    { pattern: /smooth/g, phrase: 'smoothing' },
    { pattern: /even\s+tone/g, phrase: 'even tone' },
    { pattern: /texture/g, phrase: 'texture improvement' },
    { pattern: /pore/g, phrase: 'pore refinement' }
  ];

  benefitPatterns.forEach(({ pattern, phrase }) => {
    if (pattern.test(lowerText)) {
      phrases.push(phrase);
    }
  });

  return Array.from(new Set(phrases)); // Remove duplicates
}
