import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

interface AnalysisData {
  analyses?: Array<{
    type: string;
    overall_score?: number;
    factors?: Array<{
      name: string;
      score: number;
      description: string;
      recommendation: string;
    }>;
    analysis_text?: string;
    overall_recommendations?: string;
  }>;
  overall_score?: number;
  factors?: Array<{
    name: string;
    score: number;
    description: string;
    recommendation: string;
  }>;
  analysis_text?: string;
  overall_recommendations?: string;
}

interface PDFOptions {
  analysisData: AnalysisData;
  imageBase64?: string;
  createdAt: string;
  resultId: string;
}

/**
 * Generates a PDF report from analysis data
 */
export async function generateAnalysisPDF(options: PDFOptions): Promise<void> {
  const { analysisData, imageBase64, createdAt, resultId } = options;
  
  const pdf = new jsPDF('p', 'mm', 'a4');
  const pageWidth = pdf.internal.pageSize.getWidth();
  const pageHeight = pdf.internal.pageSize.getHeight();
  const margin = 20;
  const contentWidth = pageWidth - (margin * 2);
  
  let currentY = margin;
  let pageNumber = 1;
  
  // Helper function to add watermark
  const addWatermark = () => {
    pdf.setFontSize(10);
    pdf.setTextColor(200, 200, 200);
    pdf.text('www.attractivenessscore.com', pageWidth / 2, pageHeight - 10, { align: 'center' });
  };
  
  // Helper function to add page number
  const addPageNumber = () => {
    pdf.setFontSize(10);
    pdf.setTextColor(100, 100, 100);
    const totalPages = pdf.getNumberOfPages();
    pdf.text(`Page ${pageNumber}/${totalPages}`, pageWidth - margin, pageHeight - 10, { align: 'right' });
  };
  
  // Helper function to check if we need a new page
  const checkNewPage = (requiredHeight: number) => {
    if (currentY + requiredHeight > pageHeight - 30) {
      addWatermark();
      addPageNumber();
      pdf.addPage();
      pageNumber++;
      currentY = margin;
    }
  };
  
  // Title
  pdf.setFontSize(24);
  pdf.setTextColor(0, 0, 0);
  pdf.text('Attractiveness Analysis Report', margin, currentY);
  currentY += 15;
  
  // Date
  pdf.setFontSize(12);
  pdf.setTextColor(100, 100, 100);
  pdf.text(`Generated on: ${new Date(createdAt).toLocaleDateString()}`, margin, currentY);
  currentY += 10;
  
  // Add image if available
  if (imageBase64) {
    checkNewPage(80);
    try {
      const imgWidth = 60;
      const imgHeight = 60;
      const imgX = (pageWidth - imgWidth) / 2;
      
      pdf.addImage(imageBase64, 'JPEG', imgX, currentY, imgWidth, imgHeight);
      currentY += imgHeight + 10;
    } catch (error) {
      console.error('Error adding image to PDF:', error);
    }
  }
  
  // Handle both single and multi-analysis formats
  const analyses = analysisData.analyses || [analysisData];
  
  for (const analysis of analyses) {
    if (!analysis) continue;
    
    checkNewPage(30);
    
    // Analysis type title
    if ('type' in analysis && analysis.type) {
      pdf.setFontSize(18);
      pdf.setTextColor(0, 0, 0);
      const title = getAnalysisTitle(analysis.type);
      pdf.text(title, margin, currentY);
      currentY += 12;
    }
    
    // Overall score
    if (analysis.overall_score !== undefined) {
      checkNewPage(20);
      pdf.setFontSize(16);
      pdf.setTextColor(220, 20, 60);
      pdf.text(`Overall Score: ${analysis.overall_score.toFixed(1)}/10`, margin, currentY);
      currentY += 12;
    }
    
    // Analysis text
    if (analysis.analysis_text) {
      checkNewPage(30);
      pdf.setFontSize(12);
      pdf.setTextColor(0, 0, 0);
      pdf.text('Analysis:', margin, currentY);
      currentY += 8;
      
      const analysisLines = pdf.splitTextToSize(analysis.analysis_text, contentWidth);
      for (const line of analysisLines) {
        checkNewPage(6);
        pdf.text(line, margin, currentY);
        currentY += 6;
      }
      currentY += 5;
    }
    
    // Factors
    if (analysis.factors && analysis.factors.length > 0) {
      checkNewPage(20);
      pdf.setFontSize(14);
      pdf.setTextColor(0, 0, 0);
      pdf.text('Detailed Analysis:', margin, currentY);
      currentY += 10;
      
      for (const factor of analysis.factors) {
        checkNewPage(25);
        
        // Factor name and score
        pdf.setFontSize(12);
        pdf.setTextColor(0, 0, 0);
        pdf.text(`${factor.name}: ${factor.score.toFixed(1)}/10`, margin, currentY);
        currentY += 8;
        
        // Description
        pdf.setFontSize(10);
        pdf.setTextColor(60, 60, 60);
        const descLines = pdf.splitTextToSize(factor.description, contentWidth - 10);
        for (const line of descLines) {
          checkNewPage(5);
          pdf.text(line, margin + 5, currentY);
          currentY += 5;
        }
        
        // Recommendation
        if (factor.recommendation) {
          pdf.setTextColor(0, 100, 200);
          const recLines = pdf.splitTextToSize(`Recommendation: ${factor.recommendation}`, contentWidth - 10);
          for (const line of recLines) {
            checkNewPage(5);
            pdf.text(line, margin + 5, currentY);
            currentY += 5;
          }
        }
        currentY += 5;
      }
    }
    
    // Overall recommendations
    if (analysis.overall_recommendations) {
      checkNewPage(30);
      pdf.setFontSize(14);
      pdf.setTextColor(0, 0, 0);
      pdf.text('Overall Recommendations:', margin, currentY);
      currentY += 10;
      
      pdf.setFontSize(11);
      pdf.setTextColor(0, 100, 0);
      const recLines = pdf.splitTextToSize(analysis.overall_recommendations, contentWidth);
      for (const line of recLines) {
        checkNewPage(6);
        pdf.text(line, margin, currentY);
        currentY += 6;
      }
      currentY += 10;
    }
  }
  
  // Add disclaimer on a new page
  checkNewPage(100);
  pdf.setFontSize(16);
  pdf.setTextColor(200, 0, 0);
  pdf.text('DISCLAIMER', margin, currentY);
  currentY += 15;
  
  pdf.setFontSize(11);
  pdf.setTextColor(0, 0, 0);
  const disclaimerText = `This analysis is generated by artificial intelligence for entertainment and informational purposes only. The results should not be considered as professional medical, psychological, or aesthetic advice. Beauty and attractiveness are subjective and vary greatly among individuals and cultures.

This analysis does not constitute professional consultation and should not be used as the basis for any medical, cosmetic, or surgical decisions. Individual results may vary, and the AI system may not account for all factors that contribute to attractiveness or personal appeal.

For professional advice regarding appearance, health, or self-improvement, please consult with qualified professionals in the relevant fields.

The creators and operators of this service disclaim any liability for decisions made based on this analysis.`;
  
  const disclaimerLines = pdf.splitTextToSize(disclaimerText, contentWidth);
  for (const line of disclaimerLines) {
    checkNewPage(6);
    pdf.text(line, margin, currentY);
    currentY += 6;
  }
  
  // Add watermark and page numbers to all pages
  const totalPages = pdf.getNumberOfPages();
  for (let i = 1; i <= totalPages; i++) {
    pdf.setPage(i);
    addWatermark();
    // Update page numbers
    pdf.setFontSize(10);
    pdf.setTextColor(100, 100, 100);
    pdf.text(`Page ${i}/${totalPages}`, pageWidth - margin, pageHeight - 10, { align: 'right' });
  }
  
  // Save the PDF
  pdf.save(`attractiveness-analysis-${resultId}.pdf`);
}

function getAnalysisTitle(type: string): string {
  const titles: { [key: string]: string } = {
    'attractive-score': 'Attractiveness Analysis',
    'face-shape-detector': 'Face Shape Analysis',
    'golden-ratio-face': 'Golden Ratio Analysis',
    'smile-rating': 'Smile Analysis'
  };
  return titles[type] || type;
}
