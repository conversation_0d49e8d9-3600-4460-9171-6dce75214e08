import { test, expect } from '@playwright/test';
import path from 'path';

test.describe('AttractivenessScore Main Functionality', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('should display the main page with correct title and header', async ({ page }) => {
    // Check page title
    await expect(page).toHaveTitle(/How Attractive Am I/);
    
    // Check main header
    await expect(page.locator('h1')).toContainText('How Attractive Am I?');
    
    // Check that header doesn't have animation class
    const headerSpan = page.locator('h1 span');
    await expect(headerSpan).not.toHaveClass(/animate-gradient/);
    await expect(headerSpan).toHaveClass(/bg-gradient-to-r/);
  });

  test('should display analysis options with correct title case', async ({ page }) => {
    // Check that analysis options are displayed
    await expect(page.locator('text=Choose your analysis:')).toBeVisible();
    
    // Check title case for analysis options
    await expect(page.locator('text=How Attractive Am I')).toBeVisible();
    await expect(page.locator('text=Face Shape Detector')).toBeVisible();
    await expect(page.locator('text=Golden Ratio Face')).toBeVisible();
  });

  test('should allow file upload via Choose File button', async ({ page }) => {
    // Create a test image file path
    const testImagePath = path.join(__dirname, 'fixtures', 'test-image.png');

    // Click the Choose File button
    const chooseFileButton = page.locator('button:has-text("Choose File")');
    await expect(chooseFileButton).toBeVisible();

    // Set up file chooser and upload file
    const fileChooserPromise = page.waitForEvent('filechooser');
    await chooseFileButton.click();
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(testImagePath);

    // Verify file was selected (should show image preview or filename)
    await expect(page.locator('text=test-image.png').or(page.locator('img[alt="Selected"]'))).toBeVisible({ timeout: 5000 });
  });

  test('should allow multiple analysis selection', async ({ page }) => {
    // Initially, only "How Attractive Am I" should be checked
    const attractiveCheckbox = page.locator('input[type="checkbox"]').first();
    await expect(attractiveCheckbox).toBeChecked();
    
    // Check additional analysis options
    const faceShapeCheckbox = page.locator('text=Face Shape Detector').locator('..').locator('input[type="checkbox"]');
    const goldenRatioCheckbox = page.locator('text=Golden Ratio Face').locator('..').locator('input[type="checkbox"]');
    
    await faceShapeCheckbox.check();
    await goldenRatioCheckbox.check();
    
    // Verify multiple checkboxes are selected
    await expect(attractiveCheckbox).toBeChecked();
    await expect(faceShapeCheckbox).toBeChecked();
    await expect(goldenRatioCheckbox).toBeChecked();
  });

  test('should display features section with proper headings', async ({ page }) => {
    // Check features section heading
    await expect(page.locator('h2:has-text("Why Choose Our AI Analysis?")')).toBeVisible();
    
    // Check feature cards
    await expect(page.locator('h3:has-text("AI-Powered Analysis")')).toBeVisible();
    await expect(page.locator('h3:has-text("Instant Results")')).toBeVisible();
    await expect(page.locator('h3:has-text("Privacy First")')).toBeVisible();
  });

  test('should have proper SEO elements', async ({ page }) => {
    // Check meta description
    const metaDescription = page.locator('meta[name="description"]');
    await expect(metaDescription).toHaveAttribute('content', /AI-powered facial analysis/);
    
    // Check Open Graph tags
    const ogTitle = page.locator('meta[property="og:title"]');
    await expect(ogTitle).toHaveAttribute('content', /How Attractive Am I/);
    
    const ogDescription = page.locator('meta[property="og:description"]');
    await expect(ogDescription).toHaveAttribute('content', /AI-powered facial analysis/);
    
    // Check structured data
    const structuredData = page.locator('script[type="application/ld+json"]');
    await expect(structuredData).toHaveCount(4); // website, organization, faq, webapp schemas
  });

  test('should have accessible navigation', async ({ page }) => {
    // Check that main sections have proper ARIA labels
    const featuresSection = page.locator('section[aria-labelledby="features-heading"]');
    await expect(featuresSection).toBeVisible();
    
    // Check heading hierarchy
    const h1 = page.locator('h1');
    const h2 = page.locator('h2');
    const h3 = page.locator('h3');
    
    await expect(h1).toHaveCount(1); // Should have exactly one H1
    expect(await h2.count()).toBeGreaterThan(0); // Should have H2s
    expect(await h3.count()).toBeGreaterThan(0); // Should have H3s
  });

  test('should handle drag and drop area', async ({ page }) => {
    // Check that drag and drop area is visible
    const dragArea = page.locator('div:has-text("Drag and drop or click to select an image")');
    await expect(dragArea).toBeVisible();
    
    // Check upload modes
    const uploadButton = page.locator('button:has-text("Upload Photo")');
    const cameraButton = page.locator('button:has-text("Snap Photo")');
    
    await expect(uploadButton).toBeVisible();
    await expect(cameraButton).toBeVisible();
  });

  test('should show analyze button when conditions are met', async ({ page }) => {
    // Initially, analyze button should be disabled or not visible for analysis
    // This test would need a real image upload to fully test
    
    // Check that the analyze section exists
    const analyzeSection = page.locator('text=Choose your analysis:');
    await expect(analyzeSection).toBeVisible();
  });

  test('should have proper mobile responsiveness', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Check that main elements are still visible
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('text=Choose your analysis:')).toBeVisible();
    await expect(page.locator('button:has-text("Choose File")')).toBeVisible();
    
    // Check that layout adapts
    const container = page.locator('.container').first();
    await expect(container).toBeVisible();
  });
});
