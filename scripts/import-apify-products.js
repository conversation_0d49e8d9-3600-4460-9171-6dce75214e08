// Script to fetch Amazon product data from Apify dataset and insert into Supabase
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Function to generate semantic keywords based on product name and search query
function generateSemanticKeywords(productName, searchQuery) {
  const keywords = [];
  const name = productName.toLowerCase();
  const query = searchQuery.toLowerCase();
  
  // Skin care keywords
  if (name.includes('serum') || name.includes('vitamin c') || name.includes('brightening')) {
    keywords.push('skin_radiance', 'vitamin_c_glow', 'skin_brightness');
  }
  if (name.includes('hyaluronic') || name.includes('moistur') || name.includes('hydrat')) {
    keywords.push('hydration', 'skin_elasticity');
  }
  if (name.includes('collagen') || name.includes('anti-aging') || name.includes('wrinkle')) {
    keywords.push('collagen', 'skin_elasticity', 'anti_aging');
  }
  if (name.includes('retinol') || name.includes('retinoid')) {
    keywords.push('skin_renewal', 'anti_aging');
  }
  if (name.includes('niacinamide') || name.includes('pore')) {
    keywords.push('pore_minimizer', 'skin_texture');
  }
  
  // Hair care keywords
  if (name.includes('shampoo') || name.includes('conditioner') || name.includes('hair')) {
    keywords.push('hair_health', 'hair_growth');
  }
  if (name.includes('biotin') || name.includes('keratin')) {
    keywords.push('hair_strength', 'hair_health');
  }
  
  // Beard care keywords
  if (name.includes('beard') || name.includes('mustache') || name.includes('facial hair')) {
    keywords.push('beard_care', 'facial_hair_grooming', 'masculine_grooming');
  }
  
  // Eye care keywords
  if (name.includes('eye') || name.includes('under eye') || name.includes('dark circle')) {
    keywords.push('eye_vitality', 'under_eye_care');
  }
  
  // Bone structure support (supplements)
  if (name.includes('calcium') || name.includes('vitamin d') || name.includes('bone')) {
    keywords.push('bone_structure', 'facial_structure');
  }
  
  // General beauty keywords based on search query
  if (query.includes('brightening')) keywords.push('skin_brightness', 'skin_radiance');
  if (query.includes('anti-aging')) keywords.push('anti_aging', 'skin_elasticity');
  if (query.includes('moisturizer')) keywords.push('hydration', 'skin_barrier');
  if (query.includes('cleanser')) keywords.push('skin_cleansing', 'pore_care');
  if (query.includes('sunscreen')) keywords.push('sun_protection', 'skin_health');
  
  // Remove duplicates and return
  return [...new Set(keywords)];
}

// Function to determine primary category
function determinePrimaryCategory(productName, searchQuery) {
  const name = productName.toLowerCase();
  const query = searchQuery.toLowerCase();
  
  if (name.includes('beard') || name.includes('mustache') || name.includes('facial hair')) {
    return 'beard_care';
  }
  if (name.includes('hair') || name.includes('shampoo') || name.includes('conditioner')) {
    return 'hair_care';
  }
  if (name.includes('eye') || name.includes('under eye')) {
    return 'eye_care';
  }
  if (name.includes('supplement') || name.includes('vitamin') || name.includes('biotin')) {
    return 'supplements';
  }
  
  // Default to skin care
  return 'skin_care';
}

// Function to add affiliate tag to URL
function addAffiliateTag(url, affiliateTag) {
  if (!url || typeof url !== 'string') {
    console.error('Invalid URL provided:', url);
    return null;
  }

  try {
    const urlObj = new URL(url);
    urlObj.searchParams.set('tag', affiliateTag);
    return urlObj.toString();
  } catch (e) {
    console.error('Error processing URL:', url, e.message);
    return url; // Return original URL if parsing fails
  }
}

// Function to fetch data from Apify dataset
async function fetchApifyData() {
  try {
    console.log('Fetching data from Apify dataset...');
    const response = await fetch('https://api.apify.com/v2/datasets/Cb6EwfMrMXq5Mdylw/items?format=json&clean=true');
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    console.log(`Fetched ${data.length} products from Apify dataset`);
    
    return data;
  } catch (error) {
    console.error('Error fetching Apify data:', error);
    throw error;
  }
}

// Function to process and filter data
function processData(rawData) {
  console.log('Processing and filtering data...');
  
  // Filter products: price >= 19 AND reviewCount >= 10 AND has valid URL
  const filteredData = rawData.filter(product =>
    product.productPrice >= 19 &&
    product.reviewCount >= 10 &&
    product.productUrl &&
    typeof product.productUrl === 'string'
  );
  
  console.log(`Filtered to ${filteredData.length} products (price >= $19, reviews >= 10)`);
  
  // Process each product for Supabase insertion
  const processedData = filteredData.map(product => {
    const semanticKeywords = generateSemanticKeywords(product.productName, product.searchQuery);
    const primaryCategory = determinePrimaryCategory(product.productName, product.searchQuery);
    
    return {
      product_id: product.productId,
      search_query: product.searchQuery,
      product_name: product.productName,
      product_price: product.productPrice,
      product_price_before_discount: product.productPriceBeforeDiscount || null,
      currency_code: product.priceCurrencyCode,
      currency_symbol: product.priceCurrencySymbol,
      product_url: addAffiliateTag(product.productUrl, 'amzleanmed-20'),
      product_image_url: product.productImage,
      review_count: product.reviewCount,
      is_discounted: product.isDiscountedProduct,
      is_sponsored: product.isSponsoredProduct,
      scraped_at: product.scrapedAt,
      page_number: product.pageNumber,
      semantic_keywords: semanticKeywords,
      primary_category: primaryCategory,
      affiliate_tag: 'amzleanmed-20'
    };
  });
  
  console.log(`Processed ${processedData.length} products for insertion`);
  return processedData;
}

// Function to insert data into Supabase
async function insertIntoSupabase(processedData) {
  console.log('Inserting data into Supabase...');

  // First, check for existing products to avoid duplicates
  console.log('Checking for existing products...');
  const existingProductIds = new Set();

  try {
    const { data: existingProducts, error } = await supabase
      .from('amazon_product_references')
      .select('product_id');

    if (error) {
      console.error('Error fetching existing products:', error);
    } else {
      existingProducts.forEach(product => existingProductIds.add(product.product_id));
      console.log(`Found ${existingProductIds.size} existing products in database`);
    }
  } catch (error) {
    console.error('Exception fetching existing products:', error);
  }

  // Filter out existing products
  const newProducts = processedData.filter(product => !existingProductIds.has(product.product_id));
  console.log(`${newProducts.length} new products to insert (${processedData.length - newProducts.length} duplicates skipped)`);

  if (newProducts.length === 0) {
    console.log('No new products to insert');
    return { insertedCount: 0, errorCount: 0, skippedCount: processedData.length };
  }

  // Insert in batches to avoid overwhelming the database
  const batchSize = 50; // Reduced batch size for better reliability
  let insertedCount = 0;
  let errorCount = 0;

  for (let i = 0; i < newProducts.length; i += batchSize) {
    const batch = newProducts.slice(i, i + batchSize);

    try {
      const { data, error } = await supabase
        .from('amazon_product_references')
        .insert(batch);

      if (error) {
        console.error(`Error inserting batch ${Math.floor(i/batchSize) + 1}:`, error);
        errorCount += batch.length;
      } else {
        insertedCount += batch.length;
        console.log(`Inserted batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(newProducts.length/batchSize)} (${batch.length} products)`);
      }
    } catch (error) {
      console.error(`Exception inserting batch ${Math.floor(i/batchSize) + 1}:`, error);
      errorCount += batch.length;
    }
  }

  console.log(`\nInsertion complete:`);
  console.log(`- Successfully inserted: ${insertedCount} products`);
  console.log(`- Errors: ${errorCount} products`);
  console.log(`- Duplicates skipped: ${processedData.length - newProducts.length} products`);

  return { insertedCount, errorCount, skippedCount: processedData.length - newProducts.length };
}

// Main function
async function main() {
  try {
    console.log('Starting Apify product import process...\n');
    
    // Fetch data from Apify
    const rawData = await fetchApifyData();
    
    // Process and filter data
    const processedData = processData(rawData);
    
    if (processedData.length === 0) {
      console.log('No products meet the criteria (price >= $19, reviews >= 10)');
      return;
    }
    
    // Show sample processed product
    console.log('\nSample processed product:');
    console.log(JSON.stringify(processedData[0], null, 2));
    
    // Insert into Supabase
    const result = await insertIntoSupabase(processedData);
    
    console.log('\n✅ Import process completed successfully!');
    console.log(`Total products processed: ${processedData.length}`);
    console.log(`Successfully inserted: ${result.insertedCount}`);
    console.log(`Errors: ${result.errorCount}`);
    
  } catch (error) {
    console.error('❌ Import process failed:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main, generateSemanticKeywords, determinePrimaryCategory };
