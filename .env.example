# New Clerk Keys
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key_here
CLERK_SECRET_KEY=your_clerk_secret_key_here

# Clerk URLs
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/dashboard
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/dashboard

# OpenRouter API
OPENROUTER_API_KEY=your_openrouter_api_key_here

# App URL
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Umami Analytics Configuration
NEXT_PUBLIC_UMAMI_SCRIPT_URL=https://web-umami-100125.vercel.app/script.js
NEXT_PUBLIC_UMAMI_WEBSITE_ID=your_umami_website_id_here

# OpenRouter Configuration
OPENROUTER_REFERER=https://attractive-score.vercel.app
OPENROUTER_TITLE=AttractivenessScore

# Stripe Placeholder Keys (for later implementation)
STRIPE_PUBLISHABLE_KEY=pk_test_placeholder_stripetest
STRIPE_SECRET_KEY=sk_test_placeholder_stripetest
STRIPE_WEBHOOK_SECRET=whsec_placeholder_stripetest
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_placeholder_stripetest

#Supabase
NEXT_PUBLIC_SUPABASE_URL=https://gnqlmgtuxzhwciiiixij.supabase.co
NEXT_PUBLIC_SUPABASE_PUBLISHABLE_DEFAULT_KEY=your_supabase_publishable_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# Google Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID=your_ga_measurement_id_here

# PostHog
NEXT_PUBLIC_POSTHOG_KEY=your_posthog_key_here
NEXT_PUBLIC_POSTHOG_HOST=https://us.i.posthog.com
